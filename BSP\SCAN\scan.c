#include "ti_msp_dl_config.h"
#include "board.h"
#include "scan.h"
#include "myi2c.h"



char patrol_read(uint8_t addr, uint8_t regaddr,uint8_t *Read)
{
        uint8_t i;
        IIC_Start();
        Send_Byte((addr<<1)|0);
        if( I2C_WaitAck() == 1 ) {IIC_Stop();return 1;}
        Send_Byte(regaddr);
        if( I2C_WaitAck() == 1 ) {IIC_Stop();return 2;}

        IIC_Start();
        Send_Byte((addr<<1)|1);
        if( I2C_WaitAck() == 1 ) {IIC_Stop();return 3;}
        (*Read) =(uint8_t)Read_Byte();

        IIC_Send_Ack(1);
        IIC_Stop();
        return 0;
}
