################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
BSP/SACN/%.o: ../BSP/SACN/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O2 -I"D:/TI/TI CAR" -I"D:/TI/TI CAR/Board" -I"D:/TI/TI CAR/BSP" -I"D:/TI/TI CAR/BSP/ENCOUNDER" -I"D:/TI/TI CAR/BSP/MOTOR" -I"D:/TI/TI CAR/BSP/MPU6050" -I"D:/TI/TI CAR/BSP/SCAN" -I"D:/TI/TI CAR/BSP/OLED" -I"D:/TI/TI CAR/Debug" -I"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/TI/ccs/mspm0_sdk_2_05_01_00/source" -I"D:/TI/TI CAR/BSP/IIC" -gdwarf-3 -MMD -MP -MF"BSP/SACN/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


