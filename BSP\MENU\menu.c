#include "ti_msp_dl_config.h"
#include "board.h"
#include "menu.h"
#include "key.h"
#include "OLED.h"
#include "uart.h"
#include "encounder.h"

signed int MENU_STATE_now = 0;
signed int MENU_STATE_last = 0;
extern int rel_speed_R,rel_speed_L;
extern int tar_speed_R,tar_speed_L;
extern char Serial_RxPacket[];
extern float pitch,roll,yaw;
extern int C_R;
extern int C_L;
extern int count;

int flag_clear;
int run_flag;

void MENU_INIT(void)										//菜单初始化
{
	
	OLED_Init();
	Key_Init();
	OLED_ShowString(1,5,"MPU6050");
	OLED_ShowString(2,5,"Speed:");
	OLED_ShowString(3,5,"UART:");
	
}

void MENU_STATE(int16_t *MENU_STATE_now_add ,int16_t *MENU_STATE_last_add)			//获取当前运状态
{
			*MENU_STATE_now_add = MENU_STATE_now;
			*MENU_STATE_last_add = MENU_STATE_last;
}


void MENU_SHOW(int KeyNum)
{			
			
			if(KeyNum == 1)
            {
                 MENU_STATE_now ++;
            }
			if(KeyNum == 2){MENU_STATE_now *= 10;}
			if(KeyNum == 3){MENU_STATE_now /= 10;}					             //按键操作
            if(MENU_STATE_now < 1 || MENU_STATE_now > 4000 ||(MENU_STATE_now >4 && MENU_STATE_now < 10))
            {
                MENU_STATE_now = 0;
            }
			
//			OLED_ShowNum(4,5,KeyNum,4);
			
			switch(MENU_STATE_now)													//菜单开始
			{
				
				case 0:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1,5,"MPU6050");
					OLED_ShowString(2,5,"Speed");
                    OLED_ShowString(3, 5, "UART");
					OLED_ShowString(4, 5, "RUN");
                    OLED_ShowString(1,2," ");
					OLED_ShowString(2,2," ");
                    OLED_ShowString(3,2," ");
					OLED_ShowString(4,2," ");

									
				}break;
				
				case 1:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1,5,"MPU6050");
					OLED_ShowString(2,5,"Speed");
                    OLED_ShowString(3, 5,"UART");
					OLED_ShowString(4, 5,"RUN");
					OLED_ShowString(1,2,"*");
					OLED_ShowString(2,2," ");
                    OLED_ShowString(3,2," ");
					OLED_ShowString(4,2," ");

				}break;
				
				case 2:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1,5,"MPU6050");
					OLED_ShowString(2,5,"Speed");
                    OLED_ShowString(3, 5, "UART");
					OLED_ShowString(4, 5, "RUN");
					OLED_ShowString(1,2," ");
					OLED_ShowString(2,2,"*");
                    OLED_ShowString(3,2," ");
					OLED_ShowString(4,2," ");
				}break;

				case 3:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1,5,"MPU6050");
					OLED_ShowString(2,5,"Speed");
                    OLED_ShowString(3, 5, "UART");
					OLED_ShowString(4, 5, "RUN");
					OLED_ShowString(1,2," ");
					OLED_ShowString(2,2," ");
                    OLED_ShowString(3,2,"*");
					OLED_ShowString(4,2," ");
				}break;

				case 4:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1,5,"MPU6050");
					OLED_ShowString(2,5,"Speed");
                    OLED_ShowString(3, 5, "UART");
					OLED_ShowString(4, 5, "RUN");
					OLED_ShowString(1,2," ");
					OLED_ShowString(2,2," ");
                    OLED_ShowString(3,2," ");
					OLED_ShowString(4,2,"*");
				}break;

				case 10:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}\
                    OLED_ShowString(1, 2, "pitch");
                    OLED_ShowString(2, 2, "roll");
                    OLED_ShowString(3, 2, "yaw");
                    OLED_ShowSignedNum(1, 9, pitch, 4);
                    OLED_ShowSignedNum(2, 9, roll, 4);
                    OLED_ShowSignedNum(3, 9, yaw, 4);
				}break;
				
				case 20:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 5, "Real");
                    OLED_ShowString(2, 5, "Tar");
                    OLED_ShowString(1,2," ");
					OLED_ShowString(2,2," ");

				}break;		


				case 21:
				{   
                    if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 5, "Real");
                    OLED_ShowString(2, 5, "Tar");
                    OLED_ShowString(1,2,"*");
					OLED_ShowString(2,2," ");
					
				}break;

				case 22:
				{   
                    if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 5, "Real");
                    OLED_ShowString(2, 5, "Tar");
                    OLED_ShowString(1,2," ");
					OLED_ShowString(2,2,"*");
					
				}break;

				case 200:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 5, "Real");
                    OLED_ShowString(2, 5, "Tar");
                    OLED_ShowString(1,2," ");
					OLED_ShowString(2,2," ");
					MENU_STATE_now = 20;

				}break;	
				
				case 210:
				{   
                    if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 2, "R:");
                    OLED_ShowString(2, 2, "L:");
					OLED_ShowString(3, 2, "C_R:");
					OLED_ShowString(4, 2, "C_L:");
                    OLED_ShowSignedNum(1, 4,rel_speed_R,5);
                    OLED_ShowSignedNum(2, 4,rel_speed_L,5);
					OLED_ShowSignedNum(3, 7,C_R,7);
                    OLED_ShowSignedNum(4, 7,C_L,7);
                    
				}break;
				
				case 220:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
                    OLED_ShowString(1, 2, "R:");
                    OLED_ShowString(2, 2, "L:");
                    OLED_ShowSignedNum(1, 7,tar_speed_R,5);
                    OLED_ShowSignedNum(2, 7,tar_speed_L,5);
					
				}break;

                case 30:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 2, "RX:");
                    OLED_ShowString(2, 2, Serial_RxPacket);
				}break;
				
				case 40:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 5, "Run");
					OLED_ShowString(2, 5, "stop");
					OLED_ShowString(1, 2, " ");
					OLED_ShowString(2, 2, " ");
					OLED_ShowNum(4, 10, count, 5);
				}break;

				case 41:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 5, "Run");
					OLED_ShowString(2, 5, "stop");
					OLED_ShowString(1, 2, "*");
					OLED_ShowString(2, 2, " ");
				}break;

				case 42:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 5, "Run");
					OLED_ShowString(2, 5, "stop");
					OLED_ShowString(1, 2, " ");
					OLED_ShowString(2, 2, "*");
				}break;

				case 410:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 5, "running...");
					run_flag = 1;
					MENU_STATE_now = 40;
				}break;

				case 420:
				{
					if(flag_clear == 1){OLED_Clear();flag_clear = 0;}
					OLED_ShowString(1, 5, "stopping...");
					run_flag = 0;
					MENU_STATE_now = 40;
				}break;	
			
			}																								//菜单结束
			
			if(MENU_STATE_last != MENU_STATE_now){flag_clear = 1;}						//清除标志，用于刷新屏幕
			MENU_STATE_last = MENU_STATE_now;
			
}
