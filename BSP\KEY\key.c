#include "ti_msp_dl_config.h"
#include "board.h"
#include "key.h"

void Key_Init(void)
{
    
    delay_ms(1);
}

int GetKeyNum(void)
{
    if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY1_PIN) )
    {
        delay_ms(50);
        if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY1_PIN) ){return 1;}
    }

    if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY2_PIN) )
    {
        delay_ms(50);
        if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY2_PIN) ){return 2;}
    }
        
    if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY3_PIN))
    {
        delay_ms(50);
        if(!DL_GPIO_readPins(KEY_PORT,KEY_KEY3_PIN)){return 3;}
    }

    return 0 ;
}

