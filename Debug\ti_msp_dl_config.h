/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for PWM_0 */
#define PWM_0_INST                                                         TIMG8
#define PWM_0_INST_IRQHandler                                   TIMG8_IRQHandler
#define PWM_0_INST_INT_IRQN                                     (TIMG8_INT_IRQn)
#define PWM_0_INST_CLK_FREQ                                              5000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_0_C0_PORT                                                 GPIOA
#define GPIO_PWM_0_C0_PIN                                         DL_GPIO_PIN_21
#define GPIO_PWM_0_C0_IOMUX                                      (IOMUX_PINCM46)
#define GPIO_PWM_0_C0_IOMUX_FUNC                     IOMUX_PINCM46_PF_TIMG8_CCP0
#define GPIO_PWM_0_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_0_C1_PORT                                                 GPIOA
#define GPIO_PWM_0_C1_PIN                                         DL_GPIO_PIN_22
#define GPIO_PWM_0_C1_IOMUX                                      (IOMUX_PINCM47)
#define GPIO_PWM_0_C1_IOMUX_FUNC                     IOMUX_PINCM47_PF_TIMG8_CCP1
#define GPIO_PWM_0_C1_IDX                                    DL_TIMER_CC_1_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMA0)
#define TIMER_0_INST_IRQHandler                                 TIMA0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMA0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                          (4999U)



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           40000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_40_MHZ_115200_BAUD                                      (21)
#define UART_0_FBRD_40_MHZ_115200_BAUD                                      (45)





/* Port definition for Pin Group IIC_Software */
#define IIC_Software_PORT                                                (GPIOA)

/* Defines for SCL: GPIOA.1 with pinCMx 2 on package pin 34 */
#define IIC_Software_SCL_PIN                                     (DL_GPIO_PIN_1)
#define IIC_Software_SCL_IOMUX                                    (IOMUX_PINCM2)
/* Defines for SDA: GPIOA.0 with pinCMx 1 on package pin 33 */
#define IIC_Software_SDA_PIN                                     (DL_GPIO_PIN_0)
#define IIC_Software_SDA_IOMUX                                    (IOMUX_PINCM1)
/* Port definition for Pin Group I2C_OLED */
#define I2C_OLED_PORT                                                    (GPIOA)

/* Defines for OLED_SCL: GPIOA.31 with pinCMx 6 on package pin 39 */
#define I2C_OLED_OLED_SCL_PIN                                   (DL_GPIO_PIN_31)
#define I2C_OLED_OLED_SCL_IOMUX                                   (IOMUX_PINCM6)
/* Defines for OLED_SDA: GPIOA.28 with pinCMx 3 on package pin 35 */
#define I2C_OLED_OLED_SDA_PIN                                   (DL_GPIO_PIN_28)
#define I2C_OLED_OLED_SDA_IOMUX                                   (IOMUX_PINCM3)
/* Port definition for Pin Group R */
#define R_PORT                                                           (GPIOA)

/* Defines for RA: GPIOA.27 with pinCMx 60 on package pin 31 */
#define R_RA_PIN                                                (DL_GPIO_PIN_27)
#define R_RA_IOMUX                                               (IOMUX_PINCM60)
/* Defines for RB: GPIOA.15 with pinCMx 37 on package pin 8 */
#define R_RB_PIN                                                (DL_GPIO_PIN_15)
#define R_RB_IOMUX                                               (IOMUX_PINCM37)
/* Defines for LA: GPIOA.17 with pinCMx 39 on package pin 10 */
#define L_LA_PORT                                                        (GPIOA)
#define L_LA_PIN                                                (DL_GPIO_PIN_17)
#define L_LA_IOMUX                                               (IOMUX_PINCM39)
/* Defines for LB: GPIOB.18 with pinCMx 44 on package pin 15 */
#define L_LB_PORT                                                        (GPIOB)
#define L_LB_PIN                                                (DL_GPIO_PIN_18)
#define L_LB_IOMUX                                               (IOMUX_PINCM44)
/* Port definition for Pin Group Encounder */
#define Encounder_PORT                                                   (GPIOA)

/* Defines for ELB: GPIOA.25 with pinCMx 55 on package pin 26 */
#define Encounder_ELB_PIN                                       (DL_GPIO_PIN_25)
#define Encounder_ELB_IOMUX                                      (IOMUX_PINCM55)
/* Defines for ERA: GPIOA.26 with pinCMx 59 on package pin 30 */
// pins affected by this interrupt request:["ERA","ELA"]
#define Encounder_INT_IRQN                                      (GPIOA_INT_IRQn)
#define Encounder_INT_IIDX                      (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define Encounder_ERA_IIDX                                  (DL_GPIO_IIDX_DIO26)
#define Encounder_ERA_PIN                                       (DL_GPIO_PIN_26)
#define Encounder_ERA_IOMUX                                      (IOMUX_PINCM59)
/* Defines for ERB: GPIOA.24 with pinCMx 54 on package pin 25 */
#define Encounder_ERB_PIN                                       (DL_GPIO_PIN_24)
#define Encounder_ERB_IOMUX                                      (IOMUX_PINCM54)
/* Defines for ELA: GPIOA.7 with pinCMx 14 on package pin 49 */
#define Encounder_ELA_IIDX                                   (DL_GPIO_IIDX_DIO7)
#define Encounder_ELA_PIN                                        (DL_GPIO_PIN_7)
#define Encounder_ELA_IOMUX                                      (IOMUX_PINCM14)
/* Port definition for Pin Group KEY */
#define KEY_PORT                                                         (GPIOB)

/* Defines for KEY1: GPIOB.6 with pinCMx 23 on package pin 58 */
#define KEY_KEY1_PIN                                             (DL_GPIO_PIN_6)
#define KEY_KEY1_IOMUX                                           (IOMUX_PINCM23)
/* Defines for KEY2: GPIOB.8 with pinCMx 25 on package pin 60 */
#define KEY_KEY2_PIN                                             (DL_GPIO_PIN_8)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM25)
/* Defines for KEY3: GPIOB.23 with pinCMx 51 on package pin 22 */
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_23)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM51)
/* Port definition for Pin Group Track */
#define Track_PORT                                                       (GPIOB)

/* Defines for T5: GPIOB.15 with pinCMx 32 on package pin 3 */
#define Track_T5_PIN                                            (DL_GPIO_PIN_15)
#define Track_T5_IOMUX                                           (IOMUX_PINCM32)
/* Defines for T4: GPIOB.16 with pinCMx 33 on package pin 4 */
#define Track_T4_PIN                                            (DL_GPIO_PIN_16)
#define Track_T4_IOMUX                                           (IOMUX_PINCM33)
/* Defines for T3: GPIOB.2 with pinCMx 15 on package pin 50 */
#define Track_T3_PIN                                             (DL_GPIO_PIN_2)
#define Track_T3_IOMUX                                           (IOMUX_PINCM15)
/* Defines for T2: GPIOB.3 with pinCMx 16 on package pin 51 */
#define Track_T2_PIN                                             (DL_GPIO_PIN_3)
#define Track_T2_IOMUX                                           (IOMUX_PINCM16)
/* Defines for T1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define Track_T1_PIN                                             (DL_GPIO_PIN_9)
#define Track_T1_IOMUX                                           (IOMUX_PINCM26)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_SYSCTL_CLK_init(void);
void SYSCFG_DL_PWM_0_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_UART_0_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
