/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "board.h"
#include "OLED.h"
#include "encounder.h"
#include "motor.h"
#include "bsp_mpu6050.h"
#include "scan.h"
#include "pid.h"
#include "uart.h"
#include "key.h"
#include "menu.h"
#include "track.h"


#define count_max 1800

static volatile Encoder Encoder_A;
static volatile Encoder Encoder_B;

int num;

int rel_speed_R,rel_speed_L;
int tar_speed_R,tar_speed_L;
int C_R,C_L;

int base_speed;

float pitch,roll,yaw;
uint8_t patrol_data;
int KeyNum;
int cal_R;
int cal_L;
int data_R;
int data_L;
int count;
int T1,T2,T3,T4,T5;
int main(void)
{
      
      SYSCFG_DL_init();
      OLED_Init();
      encounder_init();
      motor_init();
      MPU6050_Init();
      Serial_Init();
      NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);          //定时中断
      NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
      OLED_ShowString(1, 2, "preparing...");
      while((Encoder_A.Should_Get_Encoder_Count != 0)| (Encoder_A.Obtained_Get_Encoder_Count != 0) |(Encoder_B.Should_Get_Encoder_Count != 0) | (Encoder_B.Obtained_Get_Encoder_Count != 0))
      {
            Encoder_A.Should_Get_Encoder_Count = 0;
            Encoder_A.Obtained_Get_Encoder_Count = 0;
            Encoder_B.Should_Get_Encoder_Count = 0;
            Encoder_B.Obtained_Get_Encoder_Count = 0;
      }

      while( mpu_dmp_init() )
      {
            delay_ms(200);
      }
      OLED_Clear();

      while(1)
      {
            get_track(&T1, &T2, &T3, &T4, &T5);
            if(run_flag == 1)
            {
                  if((T1)&(!T2)&(!T3)&(!T4)&(!T5))
                  {
                        motor_setspeed(700, 650);
                  }
                  else if ((!T1)&(T2)&(!T3)&(!T4)&(!T5)) 
                  {
                        motor_setspeed(700, 650);
                  }
                  else if ((!T1)&(!T2)&(T3)&(!T4)&(!T5)) 
                  {
                        motor_setspeed(650, 650);
                  }
                  else if ((!T1)&(!T2)&(!T3)&(T4)&(!T5))
                  {
                        motor_setspeed(650, 700);
                  }
                  else if ((!T1)&(!T2)&(!T3)&(!T4)&(T5))
                  {
                        motor_setspeed(650, 700);
                  }

                  else if ((!T1)&(T2)&(T3)&(!T4)&(!T5))
                  {
                        motor_setspeed(700, 650);
                  }
                  else if ((!T1)&(!T2)&(T3)&(T4)&(!T5))
                  {
                        motor_setspeed(700, 650);
                  }

                  else if ((T1)&(T2)&(!T3)&(!T4)&(!T5))                 //左转
                  {
                        motor_setspeed(700, 0);
                        delay_ms(400);
                  }
                  else if ((T1)&(T2)&(T3)&(!T4)&(!T5))                  //左转
                  {
                        motor_setspeed(700,0);
                        delay_ms(400);
                  }
                  else if ((T1)&(T2)&(T3)&(T4)&(!T5))                   //左转
                  {
                        motor_setspeed(700, 0);
                        delay_ms(400);
                  }
                  else 
                  {
                        motor_setspeed(700, 0);
                  }
                  
            }
            else 
            {
                  motor_setspeed(0, 0);
            }
            if (count >= count_max) {run_flag = 0;}
            KeyNum = GetKeyNum();
            MENU_SHOW(KeyNum);
            
            Serial_RxFlag = 0;            //串口标志
      }
}

void GROUP1_IRQHandler(void)
{
    uint32_t gpio_interrup = 0;

	//获取中断信号
	gpio_interrup = DL_GPIO_getEnabledInterruptStatus(Encounder_PORT,Encounder_ELA_PIN|Encounder_ERA_PIN);
	
    // encoderA         左
	if((gpio_interrup & Encounder_ELA_PIN)==Encounder_ELA_PIN)
	{
		if(!DL_GPIO_readPins(Encounder_PORT,Encounder_ELB_PIN))
		{
			Encoder_A.Should_Get_Encoder_Count--;
		}
		else if(DL_GPIO_readPins(Encounder_PORT,Encounder_ELB_PIN))
		{
			Encoder_A.Should_Get_Encoder_Count++;
		}
	}

    
	// encoderB       右
	if((gpio_interrup & Encounder_ERA_PIN)==Encounder_ERA_PIN)
	{
		if(!DL_GPIO_readPins(Encounder_PORT,Encounder_ERB_PIN))
		{
			Encoder_B.Should_Get_Encoder_Count--;
		}
		else if(DL_GPIO_readPins(Encounder_PORT,Encounder_ERB_PIN))
		{
			Encoder_B.Should_Get_Encoder_Count++;
		}
	}

	DL_GPIO_clearInterruptStatus(Encounder_PORT,Encounder_ELA_PIN|Encounder_ERA_PIN);
}

//电机编码器脉冲计数
void TIMER_0_INST_IRQHandler(void)
{
      switch(DL_TimerA_getPendingInterrupt(TIMER_0_INST))
      {
            case DL_TIMER_IIDX_ZERO:

            num++;
            
            Encoder_A.Obtained_Get_Encoder_Count = Encoder_A.Should_Get_Encoder_Count;
            Encoder_B.Obtained_Get_Encoder_Count = Encoder_B.Should_Get_Encoder_Count;

            Encoder_A.Should_Get_Encoder_Count = 0;
            Encoder_B.Should_Get_Encoder_Count = 0;//* 编码器计数值清零 *//* 两个电机安装相反，所以编码器值也要相反
            C_R = Encoder_B.Obtained_Get_Encoder_Count;
            C_L = Encoder_A.Obtained_Get_Encoder_Count;
            count +=  Encoder_A.Obtained_Get_Encoder_Count;
            get_speed(&rel_speed_R, &rel_speed_L,Encoder_B.Obtained_Get_Encoder_Count, Encoder_A.Obtained_Get_Encoder_Count); 
                        //获取欧拉角
            if( mpu_dmp_get_data(&pitch,&roll,&yaw) == 0 )
            {
                  pitch = pitch;
                  roll = roll;
                  yaw = yaw;            
            }
            break;

            default:
            break;
      }
}

