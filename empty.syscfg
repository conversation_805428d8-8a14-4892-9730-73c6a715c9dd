/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const GPIO5  = GPIO.addInstance();
const GPIO6  = GPIO.addInstance();
const GPIO7  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.inputFreq    = 40;
pinFunction4.enable       = true;
pinFunction4.HFCLKMonitor = true;
pinFunction4.HFXTStartup  = 20;

GPIO1.$name                          = "IIC_Software";
GPIO1.port                           = "PORTA";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name        = "SCL";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[0].ioStructure  = "OD";
GPIO1.associatedPins[0].assignedPin  = "1";
GPIO1.associatedPins[0].pin.$assign  = "PA1";
GPIO1.associatedPins[1].$name        = "SDA";
GPIO1.associatedPins[1].initialValue = "SET";
GPIO1.associatedPins[1].ioStructure  = "OD";
GPIO1.associatedPins[1].assignedPin  = "0";
GPIO1.associatedPins[1].pin.$assign  = "PA0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                          = "I2C_OLED";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name        = "OLED_SCL";
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[0].assignedPort = "PORTA";
GPIO2.associatedPins[0].assignedPin  = "31";
GPIO2.associatedPins[0].pin.$assign  = "PA31";
GPIO2.associatedPins[1].$name        = "OLED_SDA";
GPIO2.associatedPins[1].initialValue = "SET";
GPIO2.associatedPins[1].assignedPin  = "28";
GPIO2.associatedPins[1].pin.$assign  = "PA28";

GPIO3.$name                         = "R";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name       = "RA";
GPIO3.associatedPins[0].pin.$assign = "PA27";
GPIO3.associatedPins[1].$name       = "RB";
GPIO3.associatedPins[1].pin.$assign = "PA15";

GPIO4.$name                         = "L";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name       = "LA";
GPIO4.associatedPins[0].pin.$assign = "PA17";
GPIO4.associatedPins[1].$name       = "LB";
GPIO4.associatedPins[1].pin.$assign = "PB18";

GPIO5.$name                               = "Encounder";
GPIO5.associatedPins.create(4);
GPIO5.associatedPins[0].direction         = "INPUT";
GPIO5.associatedPins[0].interruptPriority = "0";
GPIO5.associatedPins[0].polarity          = "RISE";
GPIO5.associatedPins[0].$name             = "ELB";
GPIO5.associatedPins[0].pin.$assign       = "PA25";
GPIO5.associatedPins[1].direction         = "INPUT";
GPIO5.associatedPins[1].interruptEn       = true;
GPIO5.associatedPins[1].interruptPriority = "0";
GPIO5.associatedPins[1].polarity          = "RISE";
GPIO5.associatedPins[1].$name             = "ERA";
GPIO5.associatedPins[1].pin.$assign       = "PA26";
GPIO5.associatedPins[2].direction         = "INPUT";
GPIO5.associatedPins[2].interruptPriority = "0";
GPIO5.associatedPins[2].polarity          = "RISE";
GPIO5.associatedPins[2].$name             = "ERB";
GPIO5.associatedPins[2].pin.$assign       = "PA24";
GPIO5.associatedPins[3].$name             = "ELA";
GPIO5.associatedPins[3].direction         = "INPUT";
GPIO5.associatedPins[3].interruptEn       = true;
GPIO5.associatedPins[3].interruptPriority = "0";
GPIO5.associatedPins[3].polarity          = "RISE";
GPIO5.associatedPins[3].pin.$assign       = "PA7";

GPIO6.$name                         = "KEY";
GPIO6.port                          = "PORTB";
GPIO6.associatedPins.create(3);
GPIO6.associatedPins[0].$name       = "KEY1";
GPIO6.associatedPins[0].direction   = "INPUT";
GPIO6.associatedPins[0].pin.$assign = "PB6";
GPIO6.associatedPins[1].$name       = "KEY2";
GPIO6.associatedPins[1].direction   = "INPUT";
GPIO6.associatedPins[1].pin.$assign = "PB8";
GPIO6.associatedPins[2].$name       = "KEY3";
GPIO6.associatedPins[2].direction   = "INPUT";
GPIO6.associatedPins[2].pin.$assign = "PB23";

GPIO7.$name                         = "Track";
GPIO7.port                          = "PORTB";
GPIO7.associatedPins.create(5);
GPIO7.associatedPins[0].$name       = "T5";
GPIO7.associatedPins[0].direction   = "INPUT";
GPIO7.associatedPins[0].pin.$assign = "PB15";
GPIO7.associatedPins[1].$name       = "T4";
GPIO7.associatedPins[1].direction   = "INPUT";
GPIO7.associatedPins[1].pin.$assign = "PB16";
GPIO7.associatedPins[2].$name       = "T3";
GPIO7.associatedPins[2].direction   = "INPUT";
GPIO7.associatedPins[2].pin.$assign = "PB2";
GPIO7.associatedPins[3].$name       = "T2";
GPIO7.associatedPins[3].direction   = "INPUT";
GPIO7.associatedPins[3].pin.$assign = "PB3";
GPIO7.associatedPins[4].$name       = "T1";
GPIO7.associatedPins[4].direction   = "INPUT";
GPIO7.associatedPins[4].pin.$assign = "PB9";

PWM1.$name                      = "PWM_0";
PWM1.clockDivider               = 8;
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign         = "TIMG8";
PWM1.peripheral.ccp0Pin.$assign = "PA21";
PWM1.peripheral.ccp1Pin.$assign = "PA22";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;
SYSCTL.validateClkStatus     = true;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 200;
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.interruptPriority  = "1";
TIMER1.timerPeriod        = "100ms";
TIMER1.peripheral.$assign = "TIMA0";

UART1.$name                        = "UART_0";
UART1.enabledInterrupts            = ["RX"];
UART1.targetBaudRate               = 115200;
UART1.peripheral.$assign           = "UART0";
UART1.peripheral.rxPin.$assign     = "PA11";
UART1.peripheral.txPin.$assign     = "PA10";
UART1.txPinConfig.$name            = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.txPinConfig.enableConfig     = true;
UART1.txPinConfig.internalResistor = "PULL_UP";
UART1.rxPinConfig.$name            = "ti_driverlib_gpio_GPIOPinGeneric1";
UART1.rxPinConfig.enableConfig     = true;
UART1.rxPinConfig.internalResistor = "PULL_UP";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
