#include "ti_msp_dl_config.h"
#include "board.h"
#include "pid.h"

typedef struct Cal
{
    float Kp;
    float Ki;
    float Kd;
    float error;
    float last_error;

} Cal;
static volatile Cal speed_cal;

void Cal_speed_init(float Kp,float Ki,float Kd)
{
speed_cal.Kp = Kp;
speed_cal.Ki = Ki;
speed_cal.Kd = Kd;
}
int calculate(int rel,int target)
{
    int output;
    int dif;

    speed_cal.error = rel - target;
    dif = speed_cal.last_error - speed_cal.error;
    output = speed_cal.Kp*speed_cal.error + speed_cal.Kd*dif;
    speed_cal.last_error = speed_cal.error;

    return output;
}