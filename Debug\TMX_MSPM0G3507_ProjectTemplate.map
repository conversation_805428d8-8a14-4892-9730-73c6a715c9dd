******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 18:43:19 2025

OUTPUT FILE NAME:   <TMX_MSPM0G3507_ProjectTemplate.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007749


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000090d0  00016f30  R  X
  SRAM                  20200000   00008000  00000f6e  00007092  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000090d0   000090d0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007a30   00007a30    r-x .text
  00007af0    00007af0    00001540   00001540    r-- .rodata
  00009030    00009030    000000a0   000000a0    r-- .cinit
20200000    20200000    00000d71   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    00000349   00000000    rw- .bss
  20200b4c    20200b4c    00000225   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007a30     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000006d8     menu.o (.text.MENU_SHOW)
                  00001168    00000378     OLED.o (.text.OLED_WriteCommand)
                  000014e0    00000368     OLED.o (.text.OLED_WriteData)
                  00001848    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00001bac    00000330     OLED.o (.text.OLED_ShowSignedNum)
                  00001edc    000002f8     libc.a : s_atan.c.obj (.text.atan)
                  000021d4    000002f6     OLED.o (.text.OLED_ShowNum)
                  000024ca    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000024cc    000002b0     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000277c    00000246     OLED.o (.text.OLED_Init)
                  000029c2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000029c4    00000228     empty.o (.text.main)
                  00002bec    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00002e0c    000001e8     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00002ff4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000031d0    000001c8     inv_mpu.o (.text.mpu_dmp_get_data)
                  00003398    000001bc     OLED.o (.text.OLED_Clear)
                  00003554    000001a8     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000036fc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000388e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003890    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00003a18    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00003b88    00000158     inv_mpu.o (.text.mpu_init)
                  00003ce0    00000150     myi2c.o (.text.Read_Byte)
                  00003e30    00000150     inv_mpu.o (.text.mpu_reset_fifo)
                  00003f80    00000148     inv_mpu.o (.text.mpu_load_firmware)
                  000040c8    0000013c     myi2c.o (.text.Send_Byte)
                  00004204    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00004340    00000134     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00004474    00000130     bsp_mpu6050.o (.text.MPU6050_Init)
                  000045a4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000046c4    00000114            : memory.c.obj (.text.aligned_alloc)
                  000047d8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000048e4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000049e8    00000100     bsp_mpu6050.o (.text.MPU6050_ReadData)
                  00004ae8    000000f8     libc.a : fputs.c.obj (.text.fputs)
                  00004be0    000000f8     inv_mpu.o (.text.mpu_set_bypass)
                  00004cd8    000000ec     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00004dc4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00004eac    000000e8     libc.a : memory.c.obj (.text.free)
                  00004f94    000000e8     inv_mpu.o (.text.mpu_dmp_init)
                  0000507c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00005160    000000e4     inv_mpu.o (.text.mpu_set_sample_rate)
                  00005244    000000e0     libc.a : setvbuf.c.obj (.text.setvbuf)
                  00005324    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00005400    000000dc     inv_mpu.o (.text.mpu_set_sensors)
                  000054dc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000055b4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000568c    000000d0     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000575c    000000c8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00005824    000000c4     OLED.o (.text.OLED_ShowChar)
                  000058e8    000000bc     inv_mpu.o (.text.inv_orientation_matrix_to_scalar)
                  000059a4    000000ac     myi2c.o (.text.I2C_WaitAck)
                  00005a50    000000ac     inv_mpu.o (.text.mpu_configure_fifo)
                  00005afc    000000a4     bsp_mpu6050.o (.text.MPU6050_WriteReg)
                  00005ba0    000000a4     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00005c44    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00005ce6    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00005d80    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00005e0c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005e98    00000088     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00005f20    00000088     libc.a : strcmp-armv6m.S.obj (.text:strcmp)
                  00005fa8    00000084     empty.o (.text.TIMA0_IRQHandler)
                  0000602c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000060ae    00000002     libc.a : _lock.c.obj (.text._nop)
                  000060b0    0000007c            : fclose.c.obj (.text.__TI_closefile)
                  0000612c    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000061a8    00000078     uart.o (.text.UART0_IRQHandler)
                  00006220    00000074     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006294    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00006308    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00006310    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00006384    00000074     inv_mpu.o (.text.mpu_set_lpf)
                  000063f8    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00006468    0000006c     empty.o (.text.GROUP1_IRQHandler)
                  000064d4    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  00006540    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  000065ac    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  00006618    0000006c            : getdevice.c.obj (.text.getdevice)
                  00006684    0000006c     inv_mpu.o (.text.mpu_write_mem)
                  000066f0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006758    00000068     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000067c0    00000068     board.o (.text.lc_printf)
                  00006828    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000688e    00000002     inv_mpu.o (.text.mget_ms)
                  00006890    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000068f4    00000064     libc.a : _io_perm.c.obj (.text.__TI_wrt_ok)
                  00006958    00000064            : memory.c.obj (.text.split)
                  000069bc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00006a1e    00000062     libc.a : memset16.S.obj (.text:memset)
                  00006a80    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  00006ae0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006b3c    0000005c            : printf.c.obj (.text.printf)
                  00006b98    00000058     libsysbm.a : hostread.c.obj (.text.HOSTread)
                  00006bf0    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00006c48    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006ca0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006cf8    00000058     motor.o (.text.motor_setspeed)
                  00006d50    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006da6    00000002     --HOLE-- [fill = 0]
                  00006da8    00000054     key.o (.text.GetKeyNum)
                  00006dfc    00000054     OLED.o (.text.OLED_ShowString)
                  00006e50    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00006ea2    00000052            : _printfi.c.obj (.text._ecpy)
                  00006ef4    00000050     libsysbm.a : close.c.obj (.text.close)
                  00006f44    00000050     track.o (.text.get_track)
                  00006f94    0000004c     myi2c.o (.text.IIC_Send_Ack)
                  00006fe0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0000702c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00007076    00000002     --HOLE-- [fill = 0]
                  00007078    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000070c0    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  00007108    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  00007150    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00007198    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000071dc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000721c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000725c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000729c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000072dc    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007318    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007354    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000738e    00000002     --HOLE-- [fill = 0]
                  00007390    00000038     myi2c.o (.text.IIC_Start)
                  000073c8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00007400    00000034     myi2c.o (.text.IIC_Stop)
                  00007434    00000034     libc.a : fopen.c.obj (.text.__TI_cleanup)
                  00007468    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  0000749c    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  000074d0    00000034     libc.a : exit.c.obj (.text.exit)
                  00007504    00000034            : getdevice.c.obj (.text.finddevice)
                  00007538    00000034     inv_mpu.o (.text.mpu_get_accel_fsr)
                  0000756c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  0000759c    00000030            : vsnprintf.c.obj (.text._outs)
                  000075cc    0000002e     encounder.o (.text.get_speed)
                  000075fa    00000002     --HOLE-- [fill = 0]
                  000075fc    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007628    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007654    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  00007680    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  000076a8    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000076d0    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  000076f8    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  00007720    00000028                : write.c.obj (.text.write)
                  00007748    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007770    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007794    00000024     libc.a : fputs.c.obj (.text.puts)
                  000077b8    00000022            : memccpy.c.obj (.text.memccpy)
                  000077da    00000002     --HOLE-- [fill = 0]
                  000077dc    00000020     board.o (.text.fputc)
                  000077fc    00000020     motor.o (.text.motor_init)
                  0000781c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000783a    00000002     --HOLE-- [fill = 0]
                  0000783c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007858    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007874    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007890    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  000078ac    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000078c4    00000018     uart.o (.text.Serial_Init)
                  000078dc    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000078f4    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000790a    00000002     --HOLE-- [fill = 0]
                  0000790c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007920    00000014     encounder.o (.text.encounder_init)
                  00007934    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007948    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000795a    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  0000796c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000797c    00000010     board.o (.text.delay_ms)
                  0000798c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000799c    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  000079aa    00000002     --HOLE-- [fill = 0]
                  000079ac    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000079ba    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  000079c8    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000079d6    0000000e     libsysbm.a : hostrename.c.obj (.text.strlen)
                  000079e4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000079f0    0000000c     board.o (.text.delay_us)
                  000079fc    0000000c     libc.a : memory.c.obj (.text.malloc)
                  00007a08    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007a12    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007a1c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007a2c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007a36    00000002     --HOLE-- [fill = 0]
                  00007a38    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007a48    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007a52    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007a5c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007a66    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007a70    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007a80    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007a88    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007a90    00000008     libc.a : printf.c.obj (.text._outc)
                  00007a98    00000008            : printf.c.obj (.text._outs)
                  00007aa0    00000008            : fseek.c.obj (.text.fseek)
                  00007aa8    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  00007ab0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007ab6    00000002     --HOLE-- [fill = 0]
                  00007ab8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007ac8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007ace    00000006            : exit.c.obj (.text:abort)
                  00007ad4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007ad8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007adc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007ae0    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00007ae4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00009030    000000a0     
                  00009030    0000007a     (.cinit..data.load) [load image, compression = lzss]
                  000090aa    00000002     --HOLE-- [fill = 0]
                  000090ac    0000000c     (__TI_handler_table)
                  000090b8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000090c0    00000010     (__TI_cinit_table)

.rodata    0    00007af0    00001540     
                  00007af0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000086e6    000005f0     OLED.o (.rodata.OLED_F8x16)
                  00008cd6    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00008ce0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008de1    0000004c     inv_mpu.o (.rodata.str1.8692541638590777450.1)
                  00008e2d    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.accel_axes)
                  00008e30    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008e70    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008e98    00000028     inv_mpu.o (.rodata.test)
                  00008ec0    00000026     inv_mpu.o (.rodata.str1.6385159555456066691.1)
                  00008ee6    00000026     bsp_mpu6050.o (.rodata.str1.8106437454020807143.1)
                  00008f0c    0000001d     inv_mpu.o (.rodata.str1.7250790916905202439.1)
                  00008f29    0000001b     inv_mpu.o (.rodata.reg)
                  00008f44    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00008f58    00000012     bsp_mpu6050.o (.rodata.str1.4782026291635599792.1)
                  00008f6a    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008f7b    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008f8c    0000000d     empty.o (.rodata.str1.9517790425240694019.1)
                  00008f99    00000001     --HOLE-- [fill = 0]
                  00008f9a    0000000c     inv_mpu.o (.rodata.hw)
                  00008fa6    0000000c     menu.o (.rodata.str1.9260523083322641056.1)
                  00008fb2    0000000b     menu.o (.rodata.str1.1883380180211252164.1)
                  00008fbd    00000009     bsp_mpu6050.o (.rodata.str1.8996897938050910238.1)
                  00008fc6    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00008fc8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00008fd0    00000008     menu.o (.rodata.str1.4862307748908978627.1)
                  00008fd8    00000006     menu.o (.rodata.str1.13124720451672825294.1)
                  00008fde    00000006     menu.o (.rodata.str1.9546740394641817443.1)
                  00008fe4    00000005     menu.o (.rodata.str1.13854193301767519817.1)
                  00008fe9    00000005     menu.o (.rodata.str1.15382373691547772653.1)
                  00008fee    00000005     menu.o (.rodata.str1.1645282505431347711.1)
                  00008ff3    00000005     menu.o (.rodata.str1.5102509094067202407.1)
                  00008ff8    00000005     menu.o (.rodata.str1.5155000645726596423.1)
                  00008ffd    00000005     menu.o (.rodata.str1.6857549598058878762.1)
                  00009002    00000004     menu.o (.rodata.str1.12621154128155039063.1)
                  00009006    00000004     menu.o (.rodata.str1.1955320522770775823.1)
                  0000900a    00000004     menu.o (.rodata.str1.4646809750356241979.1)
                  0000900e    00000004     menu.o (.rodata.str1.8935930992971773687.1)
                  00009012    00000004     menu.o (.rodata.str1.9335743632997171283.1)
                  00009016    00000003     inv_mpu_dmp_motion_driver.o (.rodata..L__const.dmp_set_orientation.gyro_axes)
                  00009019    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  0000901c    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000901f    00000003     menu.o (.rodata.str1.13617062986815958976.1)
                  00009022    00000003     menu.o (.rodata.str1.6617442809723977961.1)
                  00009025    00000002     menu.o (.rodata.str1.12110183915172842339.1)
                  00009027    00000002     menu.o (.rodata.str1.16312125044762562515.1)
                  00009029    00000002     libc.a : fputs.c.obj (.rodata.str1.16607721268415185390.1)
                  0000902b    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    00000349     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    000000bc     (.common:gTIMER_0Backup)
                  202009dc    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200a7c    00000064     (.common:Serial_RxPacket)
                  20200ae0    00000008     empty.o (.bss.Encoder_A)
                  20200ae8    00000008     empty.o (.bss.Encoder_B)
                  20200af0    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200af8    00000004     libc.a : memory.c.obj (.bss.sys_free)
                  20200afc    00000004     (.common:C_L)
                  20200b00    00000004     (.common:C_R)
                  20200b04    00000004     (.common:KeyNum)
                  20200b08    00000004     (.common:T1)
                  20200b0c    00000004     (.common:T2)
                  20200b10    00000004     (.common:T3)
                  20200b14    00000004     (.common:T4)
                  20200b18    00000004     (.common:T5)
                  20200b1c    00000004     (.common:count)
                  20200b20    00000004     (.common:flag_clear)
                  20200b24    00000004     (.common:num)
                  20200b28    00000004     (.common:pitch)
                  20200b2c    00000004     (.common:rel_speed_L)
                  20200b30    00000004     (.common:rel_speed_R)
                  20200b34    00000004     (.common:roll)
                  20200b38    00000004     (.common:run_flag)
                  20200b3c    00000004     (.common:tar_speed_L)
                  20200b40    00000004     (.common:tar_speed_R)
                  20200b44    00000004     (.common:yaw)
                  20200b48    00000001     (.common:Serial_RxFlag)

.data      0    20200b4c    00000225     UNINITIALIZED
                  20200b4c    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200c3c    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200cb4    00000050                : host_device.c.obj (.data._stream)
                  20200d04    0000002c     inv_mpu.o (.data.st)
                  20200d30    00000009     inv_mpu.o (.data.gyro_orientation)
                  20200d39    00000001     uart.o (.data.UART0_IRQHandler.RxState)
                  20200d3a    00000001     uart.o (.data.UART0_IRQHandler.pRxPacket)
                  20200d3b    00000001     --HOLE--
                  20200d3c    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200d44    00000004     menu.o (.data.MENU_STATE_last)
                  20200d48    00000004     menu.o (.data.MENU_STATE_now)
                  20200d4c    00000004     libc.a : defs.c.obj (.data.__TI_ft_end)
                  20200d50    00000004            : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200d54    00000004            : _lock.c.obj (.data._lock)
                  20200d58    00000004            : _lock.c.obj (.data._unlock)
                  20200d5c    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.0)
                  20200d60    00000004     inv_mpu_dmp_motion_driver.o (.data.dmp.1)
                  20200d64    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.2)
                  20200d66    00000001     inv_mpu_dmp_motion_driver.o (.data.dmp.5)
                  20200d67    00000001     --HOLE--
                  20200d68    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.3)
                  20200d6a    00000002     --HOLE--
                  20200d6c    00000002     inv_mpu_dmp_motion_driver.o (.data.dmp.4)
                  20200d6e    00000002     --HOLE--
                  20200d70    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             800     86        188    
       empty.o                        792     13        84     
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1598    291       272    
                                                               
    .\BSP\ENCOUNDER\
       encounder.o                    66      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         66      0         0      
                                                               
    .\BSP\IIC\
       myi2c.o                        1008    0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1008    0         0      
                                                               
    .\BSP\KEY\
       key.o                          84      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         84      0         0      
                                                               
    .\BSP\MENU\
       menu.o                         1752    103       16     
    +--+------------------------------+-------+---------+---------+
       Total:                         1752    103       16     
                                                               
    .\BSP\MOTOR\
       motor.o                        120     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         120     0         0      
                                                               
    .\BSP\MPU6050\
       inv_mpu_dmp_motion_driver.o    1788    3068      15     
       inv_mpu.o                      4026    222       53     
       bsp_mpu6050.o                  724     65        0      
    +--+------------------------------+-------+---------+---------+
       Total:                         6538    3355      68     
                                                               
    .\BSP\OLED\
       OLED.o                         4640    1520      0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4640    1520      0      
                                                               
    .\BSP\TRACK\
       track.o                        80      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         80      0         0      
                                                               
    .\BSP\UART\
       uart.o                         144     0         103    
    +--+------------------------------+-------+---------+---------+
       Total:                         144     0         103    
                                                               
    .\Board\
       board.o                        164     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         164     0         0      
                                                               
    D:/TI/ccs/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1076    0         0      
                                                               
    D:\TI\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       memory.c.obj                   702     0         5      
       defs.c.obj                     0       0         404    
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       fputs.c.obj                    284     2         0      
       aeabi_ctype.S.obj              0       257       0      
       setvbuf.c.obj                  224     0         0      
       s_scalbn.c.obj                 216     0         0      
       getdevice.c.obj                160     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       fclose.c.obj                   124     0         0      
       fseek.c.obj                    116     0         0      
       printf.c.obj                   108     0         0      
       _io_perm.c.obj                 100     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       fflush.c.obj                   82      0         0      
       exit.c.obj                     58      0         8      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       fopen.c.obj                    52      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         10284   357       429    
                                                               
    D:\TI\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104     0         288    
       host_device.c.obj              0       0         200    
       hostrename.c.obj               136     0         0      
       hostlseek.c.obj                108     0         0      
       hostopen.c.obj                 96      0         8      
       hostread.c.obj                 88      0         0      
       hostwrite.c.obj                88      0         0      
       close.c.obj                    80      0         0      
       hostclose.c.obj                72      0         0      
       hostunlink.c.obj               72      0         0      
       unlink.c.obj                   44      0         0      
       lseek.c.obj                    40      0         0      
       write.c.obj                    40      0         0      
       remove.c.obj                   8       0         0      
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         980     0         496    
                                                               
    D:\TI\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2714    0         0      
                                                               
       Heap:                          0       0         2048   
       Stack:                         0       0         512    
       Linker Generated:              0       158       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31248   5784      3944   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000090c0 records: 2, size/record: 8, table size: 16
	.data: load addr=00009030, load size=0000007a bytes, run addr=20200b4c, run size=00000225 bytes, compression=lzss
	.bss: load addr=000090b8, load size=00000008 bytes, run addr=20200800, run size=00000349 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000090ac records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000036fd     00007a1c     00007a1a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000507d     00007a38     00007a34   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007a50          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007a64          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007a86          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007acc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   000047d9     00007a70     00007a6e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00003707     00007ab8     00007ab4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007ada          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[4 trampolines]
[9 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000024cb  ADC0_IRQHandler                      
000024cb  ADC1_IRQHandler                      
000024cb  AES_IRQHandler                       
00007ad4  C$$EXIT                              
000074c9  C$$IO$$                              
000024cb  CANFD0_IRQHandler                    
20200afc  C_L                                  
20200b00  C_R                                  
000024cb  DAC0_IRQHandler                      
00007a09  DL_Common_delayCycles                
00005325  DL_SYSCTL_configSYSPLL               
00006891  DL_SYSCTL_setHFCLKSourceHFXTParams   
00007199  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000048e5  DL_Timer_initFourCCPWMMode           
00004dc5  DL_Timer_initTimerMode               
0000783d  DL_Timer_setCaptCompUpdateMethod     
000078ad  DL_Timer_setCaptureCompareOutCtl     
0000796d  DL_Timer_setCaptureCompareValue      
00007859  DL_Timer_setClockConfig              
00007079  DL_UART_init                         
00007949  DL_UART_setClockConfig               
000024cb  DMA_IRQHandler                       
000024cb  Default_Handler                      
000024cb  GROUP0_IRQHandler                    
00006469  GROUP1_IRQHandler                    
00006da9  GetKeyNum                            
000070c1  HOSTclose                            
00007ad5  HOSTexit                             
000064d5  HOSTlseek                            
00006a81  HOSTopen                             
00006b99  HOSTread                             
00006541  HOSTrename                           
00007109  HOSTunlink                           
00006bf1  HOSTwrite                            
000024cb  HardFault_Handler                    
000024cb  I2C0_IRQHandler                      
000024cb  I2C1_IRQHandler                      
000059a5  I2C_WaitAck                          
00006f95  IIC_Send_Ack                         
00007391  IIC_Start                            
00007401  IIC_Stop                             
20200b04  KeyNum                               
00000a91  MENU_SHOW                            
20200d44  MENU_STATE_last                      
20200d48  MENU_STATE_now                       
00004475  MPU6050_Init                         
000049e9  MPU6050_ReadData                     
00005afd  MPU6050_WriteReg                     
000024cb  NMI_Handler                          
00003399  OLED_Clear                           
000086e6  OLED_F8x16                           
0000277d  OLED_Init                            
00005825  OLED_ShowChar                        
000021d5  OLED_ShowNum                         
00001bad  OLED_ShowSignedNum                   
00006dfd  OLED_ShowString                      
00001169  OLED_WriteCommand                    
000014e1  OLED_WriteData                       
000024cb  PendSV_Handler                       
000024cb  RTC_IRQHandler                       
00003ce1  Read_Byte                            
00007add  Reset_Handler                        
000024cb  SPI0_IRQHandler                      
000024cb  SPI1_IRQHandler                      
000024cb  SVC_Handler                          
0000575d  SYSCFG_DL_GPIO_init                  
00005d81  SYSCFG_DL_PWM_0_init                 
00007681  SYSCFG_DL_SYSCTL_CLK_init            
00006221  SYSCFG_DL_SYSCTL_init                
00006fe1  SYSCFG_DL_TIMER_0_init               
000063f9  SYSCFG_DL_UART_0_init                
000075fd  SYSCFG_DL_init                       
00007151  SYSCFG_DL_initPower                  
000040c9  Send_Byte                            
000078c5  Serial_Init                          
20200b48  Serial_RxFlag                        
20200a7c  Serial_RxPacket                      
000024cb  SysTick_Handler                      
20200b08  T1                                   
20200b0c  T2                                   
20200b10  T3                                   
20200b14  T4                                   
20200b18  T5                                   
00005fa9  TIMA0_IRQHandler                     
000024cb  TIMA1_IRQHandler                     
000024cb  TIMG0_IRQHandler                     
000024cb  TIMG12_IRQHandler                    
000024cb  TIMG6_IRQHandler                     
000024cb  TIMG7_IRQHandler                     
000024cb  TIMG8_IRQHandler                     
000061a9  UART0_IRQHandler                     
000024cb  UART1_IRQHandler                     
000024cb  UART2_IRQHandler                     
000024cb  UART3_IRQHandler                     
20200800  _CIOBUF_                             
20200800  __CIOBUF_                            
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000800  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000090c0  __TI_CINIT_Base                      
000090d0  __TI_CINIT_Limit                     
000090d0  __TI_CINIT_Warm                      
000090ac  __TI_Handler_Table_Base              
000090b8  __TI_Handler_Table_Limit             
00007319  __TI_auto_init_nobinit_nopinit       
00007435  __TI_cleanup                         
20200d3c  __TI_cleanup_ptr                     
000060b1  __TI_closefile                       
0000612d  __TI_decompress_lzss                 
0000795b  __TI_decompress_none                 
00006e51  __TI_doflush                         
20200d40  __TI_dtors_ptr                       
20200d4c  __TI_ft_end                          
00006c49  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00007469  __TI_readmsg                         
00000000  __TI_static_base__                   
202009dc  __TI_tmpnams                         
0000749d  __TI_writemsg                        
000068f5  __TI_wrt_ok                          
000078f5  __TI_zero_init_nomemset              
00003707  __adddf3                             
000055bf  __addsf3                             
00008ce0  __aeabi_ctype_table_                 
00008ce0  __aeabi_ctype_table_C                
00006311  __aeabi_d2f                          
0000702d  __aeabi_d2iz                         
00003707  __aeabi_dadd                         
000069bd  __aeabi_dcmpeq                       
000069f9  __aeabi_dcmpge                       
00006a0d  __aeabi_dcmpgt                       
000069e5  __aeabi_dcmple                       
000069d1  __aeabi_dcmplt                       
000047d9  __aeabi_ddiv                         
0000507d  __aeabi_dmul                         
000036fd  __aeabi_dsub                         
20200d50  __aeabi_errno                        
00006309  __aeabi_errno_addr                   
0000721d  __aeabi_f2d                          
000073c9  __aeabi_f2iz                         
000055bf  __aeabi_fadd                         
0000602d  __aeabi_fdiv                         
00005e0d  __aeabi_fmul                         
000055b5  __aeabi_fsub                         
00007629  __aeabi_i2d                          
000072dd  __aeabi_i2f                          
00006d51  __aeabi_idiv                         
000029c3  __aeabi_idiv0                        
00006d51  __aeabi_idivmod                      
0000388f  __aeabi_ldiv0                        
0000781d  __aeabi_llsl                         
00007771  __aeabi_lmul                         
000079e5  __aeabi_memclr                       
000079e5  __aeabi_memclr4                      
000079e5  __aeabi_memclr8                      
00007a89  __aeabi_memcpy                       
00007a89  __aeabi_memcpy4                      
00007a89  __aeabi_memcpy8                      
000079ad  __aeabi_memset                       
000079ad  __aeabi_memset4                      
000079ad  __aeabi_memset8                      
000076a9  __aeabi_ui2f                         
000071dd  __aeabi_uidiv                        
000071dd  __aeabi_uidivmod                     
0000790d  __aeabi_uldivmod                     
0000781d  __ashldi3                            
ffffffff  __binit__                            
000066f1  __cmpdf2                             
000047d9  __divdf3                             
0000602d  __divsf3                             
000066f1  __eqdf2                              
0000721d  __extendsfdf2                        
0000702d  __fixdfsi                            
000073c9  __fixsfsi                            
00007629  __floatsidf                          
000072dd  __floatsisf                          
000076a9  __floatunsisf                        
00006295  __gedf2                              
00006295  __gtdf2                              
000066f1  __ledf2                              
000066f1  __ltdf2                              
UNDEFED   __mpu_init                           
0000507d  __muldf3                             
00007771  __muldi3                             
00007355  __muldsi3                            
00005e0d  __mulsf3                             
000066f1  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000036fd  __subdf3                             
000055b5  __subsf3                             
00006311  __truncdfsf2                         
00005c45  __udivmoddi4                         
00007749  _c_int00_noargs                      
20200c3c  _device                              
20200b4c  _ftable                              
20200d54  _lock                                
000060af  _nop                                 
20200cb4  _stream                              
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00007ae1  _system_pre_init                     
20200d58  _unlock                              
00007acf  abort                                
000046c5  aligned_alloc                        
00001849  asin                                 
00001849  asinl                                
00001edd  atan                                 
00003891  atan2                                
00003891  atan2l                               
00001edd  atanl                                
0000725d  atoi                                 
ffffffff  binit                                
00006ef5  close                                
20200b1c  count                                
0000797d  delay_ms                             
000079f1  delay_us                             
000024cd  dmp_enable_feature                   
00007875  dmp_load_motion_driver_firmware      
00003555  dmp_read_fifo                        
00006759  dmp_set_fifo_rate                    
00004cd9  dmp_set_orientation                  
00004341  dmp_set_tap_thresh                   
00007921  encounder_init                       
000074d1  exit                                 
00007505  finddevice                           
20200b20  flag_clear                           
000077dd  fputc                                
00004ae9  fputs                                
00004ead  free                                 
00006ae1  frexp                                
00006ae1  frexpl                               
00007aa1  fseek                                
000065ad  fseeko                               
20200920  gTIMER_0Backup                       
000075cd  get_speed                            
00006f45  get_track                            
00006619  getdevice                            
00008f9a  hw                                   
00000000  interruptVectors                     
000058e9  inv_orientation_matrix_to_scalar     
000067c1  lc_printf                            
000054dd  ldexp                                
000054dd  ldexpl                               
000076f9  lseek                                
000029c5  main                                 
000079fd  malloc                               
000046c5  memalign                             
000077b9  memccpy                              
00005ce7  memcpy                               
00006a1f  memset                               
0000688f  mget_ms                              
000077fd  motor_init                           
00006cf9  motor_setspeed                       
00005a51  mpu_configure_fifo                   
000031d1  mpu_dmp_get_data                     
00004f95  mpu_dmp_init                         
00007539  mpu_get_accel_fsr                    
00003b89  mpu_init                             
00003f81  mpu_load_firmware                    
00002e0d  mpu_lp_accel_mode                    
00005ba1  mpu_read_fifo_stream                 
00003e31  mpu_reset_fifo                       
00005e99  mpu_set_accel_fsr                    
00004be1  mpu_set_bypass                       
0000568d  mpu_set_dmp_state                    
00006385  mpu_set_lpf                          
00005161  mpu_set_sample_rate                  
00005401  mpu_set_sensors                      
00006685  mpu_write_mem                        
20200b24  num                                  
20200af0  parmbuf                              
20200b28  pitch                                
00006b3d  printf                               
00007795  puts                                 
00008f29  reg                                  
20200b2c  rel_speed_L                          
20200b30  rel_speed_R                          
00007aa9  remove                               
20200b34  roll                                 
20200b38  run_flag                             
000054dd  scalbn                               
000054dd  scalbnl                              
00005245  setvbuf                              
00003a19  sqrt                                 
00003a19  sqrtl                                
00005f21  strcmp                               
20200b3c  tar_speed_L                          
20200b40  tar_speed_R                          
00008e98  test                                 
00007655  unlink                               
0000729d  vsnprintf                            
0000798d  wcslen                               
00007721  write                                
20200b44  yaw                                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000800  __SYSMEM_SIZE                        
00000a91  MENU_SHOW                            
00001169  OLED_WriteCommand                    
000014e1  OLED_WriteData                       
00001849  asin                                 
00001849  asinl                                
00001bad  OLED_ShowSignedNum                   
00001edd  atan                                 
00001edd  atanl                                
000021d5  OLED_ShowNum                         
000024cb  ADC0_IRQHandler                      
000024cb  ADC1_IRQHandler                      
000024cb  AES_IRQHandler                       
000024cb  CANFD0_IRQHandler                    
000024cb  DAC0_IRQHandler                      
000024cb  DMA_IRQHandler                       
000024cb  Default_Handler                      
000024cb  GROUP0_IRQHandler                    
000024cb  HardFault_Handler                    
000024cb  I2C0_IRQHandler                      
000024cb  I2C1_IRQHandler                      
000024cb  NMI_Handler                          
000024cb  PendSV_Handler                       
000024cb  RTC_IRQHandler                       
000024cb  SPI0_IRQHandler                      
000024cb  SPI1_IRQHandler                      
000024cb  SVC_Handler                          
000024cb  SysTick_Handler                      
000024cb  TIMA1_IRQHandler                     
000024cb  TIMG0_IRQHandler                     
000024cb  TIMG12_IRQHandler                    
000024cb  TIMG6_IRQHandler                     
000024cb  TIMG7_IRQHandler                     
000024cb  TIMG8_IRQHandler                     
000024cb  UART1_IRQHandler                     
000024cb  UART2_IRQHandler                     
000024cb  UART3_IRQHandler                     
000024cd  dmp_enable_feature                   
0000277d  OLED_Init                            
000029c3  __aeabi_idiv0                        
000029c5  main                                 
00002e0d  mpu_lp_accel_mode                    
000031d1  mpu_dmp_get_data                     
00003399  OLED_Clear                           
00003555  dmp_read_fifo                        
000036fd  __aeabi_dsub                         
000036fd  __subdf3                             
00003707  __adddf3                             
00003707  __aeabi_dadd                         
0000388f  __aeabi_ldiv0                        
00003891  atan2                                
00003891  atan2l                               
00003a19  sqrt                                 
00003a19  sqrtl                                
00003b89  mpu_init                             
00003ce1  Read_Byte                            
00003e31  mpu_reset_fifo                       
00003f81  mpu_load_firmware                    
000040c9  Send_Byte                            
00004341  dmp_set_tap_thresh                   
00004475  MPU6050_Init                         
000046c5  aligned_alloc                        
000046c5  memalign                             
000047d9  __aeabi_ddiv                         
000047d9  __divdf3                             
000048e5  DL_Timer_initFourCCPWMMode           
000049e9  MPU6050_ReadData                     
00004ae9  fputs                                
00004be1  mpu_set_bypass                       
00004cd9  dmp_set_orientation                  
00004dc5  DL_Timer_initTimerMode               
00004ead  free                                 
00004f95  mpu_dmp_init                         
0000507d  __aeabi_dmul                         
0000507d  __muldf3                             
00005161  mpu_set_sample_rate                  
00005245  setvbuf                              
00005325  DL_SYSCTL_configSYSPLL               
00005401  mpu_set_sensors                      
000054dd  ldexp                                
000054dd  ldexpl                               
000054dd  scalbn                               
000054dd  scalbnl                              
000055b5  __aeabi_fsub                         
000055b5  __subsf3                             
000055bf  __addsf3                             
000055bf  __aeabi_fadd                         
0000568d  mpu_set_dmp_state                    
0000575d  SYSCFG_DL_GPIO_init                  
00005825  OLED_ShowChar                        
000058e9  inv_orientation_matrix_to_scalar     
000059a5  I2C_WaitAck                          
00005a51  mpu_configure_fifo                   
00005afd  MPU6050_WriteReg                     
00005ba1  mpu_read_fifo_stream                 
00005c45  __udivmoddi4                         
00005ce7  memcpy                               
00005d81  SYSCFG_DL_PWM_0_init                 
00005e0d  __aeabi_fmul                         
00005e0d  __mulsf3                             
00005e99  mpu_set_accel_fsr                    
00005f21  strcmp                               
00005fa9  TIMA0_IRQHandler                     
0000602d  __aeabi_fdiv                         
0000602d  __divsf3                             
000060af  _nop                                 
000060b1  __TI_closefile                       
0000612d  __TI_decompress_lzss                 
000061a9  UART0_IRQHandler                     
00006221  SYSCFG_DL_SYSCTL_init                
00006295  __gedf2                              
00006295  __gtdf2                              
00006309  __aeabi_errno_addr                   
00006311  __aeabi_d2f                          
00006311  __truncdfsf2                         
00006385  mpu_set_lpf                          
000063f9  SYSCFG_DL_UART_0_init                
00006469  GROUP1_IRQHandler                    
000064d5  HOSTlseek                            
00006541  HOSTrename                           
000065ad  fseeko                               
00006619  getdevice                            
00006685  mpu_write_mem                        
000066f1  __cmpdf2                             
000066f1  __eqdf2                              
000066f1  __ledf2                              
000066f1  __ltdf2                              
000066f1  __nedf2                              
00006759  dmp_set_fifo_rate                    
000067c1  lc_printf                            
0000688f  mget_ms                              
00006891  DL_SYSCTL_setHFCLKSourceHFXTParams   
000068f5  __TI_wrt_ok                          
000069bd  __aeabi_dcmpeq                       
000069d1  __aeabi_dcmplt                       
000069e5  __aeabi_dcmple                       
000069f9  __aeabi_dcmpge                       
00006a0d  __aeabi_dcmpgt                       
00006a1f  memset                               
00006a81  HOSTopen                             
00006ae1  frexp                                
00006ae1  frexpl                               
00006b3d  printf                               
00006b99  HOSTread                             
00006bf1  HOSTwrite                            
00006c49  __TI_ltoa                            
00006cf9  motor_setspeed                       
00006d51  __aeabi_idiv                         
00006d51  __aeabi_idivmod                      
00006da9  GetKeyNum                            
00006dfd  OLED_ShowString                      
00006e51  __TI_doflush                         
00006ef5  close                                
00006f45  get_track                            
00006f95  IIC_Send_Ack                         
00006fe1  SYSCFG_DL_TIMER_0_init               
0000702d  __aeabi_d2iz                         
0000702d  __fixdfsi                            
00007079  DL_UART_init                         
000070c1  HOSTclose                            
00007109  HOSTunlink                           
00007151  SYSCFG_DL_initPower                  
00007199  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000071dd  __aeabi_uidiv                        
000071dd  __aeabi_uidivmod                     
0000721d  __aeabi_f2d                          
0000721d  __extendsfdf2                        
0000725d  atoi                                 
0000729d  vsnprintf                            
000072dd  __aeabi_i2f                          
000072dd  __floatsisf                          
00007319  __TI_auto_init_nobinit_nopinit       
00007355  __muldsi3                            
00007391  IIC_Start                            
000073c9  __aeabi_f2iz                         
000073c9  __fixsfsi                            
00007401  IIC_Stop                             
00007435  __TI_cleanup                         
00007469  __TI_readmsg                         
0000749d  __TI_writemsg                        
000074c9  C$$IO$$                              
000074d1  exit                                 
00007505  finddevice                           
00007539  mpu_get_accel_fsr                    
000075cd  get_speed                            
000075fd  SYSCFG_DL_init                       
00007629  __aeabi_i2d                          
00007629  __floatsidf                          
00007655  unlink                               
00007681  SYSCFG_DL_SYSCTL_CLK_init            
000076a9  __aeabi_ui2f                         
000076a9  __floatunsisf                        
000076f9  lseek                                
00007721  write                                
00007749  _c_int00_noargs                      
00007771  __aeabi_lmul                         
00007771  __muldi3                             
00007795  puts                                 
000077b9  memccpy                              
000077dd  fputc                                
000077fd  motor_init                           
0000781d  __aeabi_llsl                         
0000781d  __ashldi3                            
0000783d  DL_Timer_setCaptCompUpdateMethod     
00007859  DL_Timer_setClockConfig              
00007875  dmp_load_motion_driver_firmware      
000078ad  DL_Timer_setCaptureCompareOutCtl     
000078c5  Serial_Init                          
000078f5  __TI_zero_init_nomemset              
0000790d  __aeabi_uldivmod                     
00007921  encounder_init                       
00007949  DL_UART_setClockConfig               
0000795b  __TI_decompress_none                 
0000796d  DL_Timer_setCaptureCompareValue      
0000797d  delay_ms                             
0000798d  wcslen                               
000079ad  __aeabi_memset                       
000079ad  __aeabi_memset4                      
000079ad  __aeabi_memset8                      
000079e5  __aeabi_memclr                       
000079e5  __aeabi_memclr4                      
000079e5  __aeabi_memclr8                      
000079f1  delay_us                             
000079fd  malloc                               
00007a09  DL_Common_delayCycles                
00007a89  __aeabi_memcpy                       
00007a89  __aeabi_memcpy4                      
00007a89  __aeabi_memcpy8                      
00007aa1  fseek                                
00007aa9  remove                               
00007acf  abort                                
00007ad4  C$$EXIT                              
00007ad5  HOSTexit                             
00007add  Reset_Handler                        
00007ae1  _system_pre_init                     
000086e6  OLED_F8x16                           
00008ce0  __aeabi_ctype_table_                 
00008ce0  __aeabi_ctype_table_C                
00008e98  test                                 
00008f29  reg                                  
00008f9a  hw                                   
000090ac  __TI_Handler_Table_Base              
000090b8  __TI_Handler_Table_Limit             
000090c0  __TI_CINIT_Base                      
000090d0  __TI_CINIT_Limit                     
000090d0  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
20200800  _CIOBUF_                             
20200800  __CIOBUF_                            
20200920  gTIMER_0Backup                       
202009dc  __TI_tmpnams                         
20200a7c  Serial_RxPacket                      
20200af0  parmbuf                              
20200afc  C_L                                  
20200b00  C_R                                  
20200b04  KeyNum                               
20200b08  T1                                   
20200b0c  T2                                   
20200b10  T3                                   
20200b14  T4                                   
20200b18  T5                                   
20200b1c  count                                
20200b20  flag_clear                           
20200b24  num                                  
20200b28  pitch                                
20200b2c  rel_speed_L                          
20200b30  rel_speed_R                          
20200b34  roll                                 
20200b38  run_flag                             
20200b3c  tar_speed_L                          
20200b40  tar_speed_R                          
20200b44  yaw                                  
20200b48  Serial_RxFlag                        
20200b4c  _ftable                              
20200c3c  _device                              
20200cb4  _stream                              
20200d3c  __TI_cleanup_ptr                     
20200d40  __TI_dtors_ptr                       
20200d44  MENU_STATE_last                      
20200d48  MENU_STATE_now                       
20200d4c  __TI_ft_end                          
20200d50  __aeabi_errno                        
20200d54  _lock                                
20200d58  _unlock                              
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[310 symbols]
