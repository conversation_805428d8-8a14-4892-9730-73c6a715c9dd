#include "ti_msp_dl_config.h"
#include "board.h"
#include "track.h"

void get_track(int *T1,int *T2,int *T3,int *T4,int *T5)
{
    (*T1) = !DL_GPIO_readPins(Track_PORT,Track_T1_PIN);
    (*T2) = !DL_GPIO_readPins(Track_PORT,Track_T2_PIN);
    (*T3) = !DL_GPIO_readPins(Track_PORT,Track_T3_PIN);
    (*T4) = !DL_GPIO_readPins(Track_PORT,Track_T4_PIN);
    (*T5) = !DL_GPIO_readPins(Track_PORT,Track_T5_PIN);    
}