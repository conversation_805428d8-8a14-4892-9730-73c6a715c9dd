/*
 * 立创开发板软硬件资料与相关扩展板软硬件资料官网全部开源
 * 开发板官网：www.lckfb.com
 * 文档网站：wiki.lckfb.com
 * 技术支持常驻论坛，任何技术问题欢迎随时交流学习
 * 嘉立创社区问答：https://www.jlc-bbs.com/lckfb
 * 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
 * 不靠卖板赚钱，以培养中国工程师为己任
 */
#include "ti_msp_dl_config.h"
#include "motor.h"
#include "encounder.h"

#define wheel_cir   50 //轮子直径，单位mm

/******************************************************************
 * 函 数 名 称：Motor_Get_Encoder
 * 函 数 说 明：获取编码器的值
 * 函 数 形 参：dir=0获取左轮编码器值  dir=1获取右轮编码器值
 * 函 数 返 回：返回对应的编码器值
 * 作       者：LCKFB
 * 备       注：无
******************************************************************/

void encounder_init(void)
{

    NVIC_ClearPendingIRQ(Encounder_INT_IRQN);
    NVIC_EnableIRQ(Encounder_INT_IRQN);
}

void get_speed(int *speed_R,int *speed_L,int count_R, int count_L)
{
    (*speed_R) = count_R*10*wheel_cir/11;
    (*speed_L) = count_L*10*wheel_cir/11;
}




