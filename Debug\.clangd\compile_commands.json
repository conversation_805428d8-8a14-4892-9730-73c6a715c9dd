[{"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/IIC/myi2c.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/MPU6050/bsp_mpu6050.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/MPU6050/inv_mpu.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/MPU6050/inv_mpu_dmp_motion_driver.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/OLED/OLED.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/Board/board.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/empty.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/MOTOR/motor.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/ENCOUNDER/encounder.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/SCAN/scan.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/PID/pid.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/UART/uart.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/MENU/menu.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/KEY/key.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/BSP/TRACK/track.c"}, {"directory": "D:/TI/TI CAR/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"D:/TI/TI CAR\" -I\"D:/TI/TI CAR/Board\" -I\"D:/TI/TI CAR/BSP\" -I\"D:/TI/TI CAR/BSP/TRACK\" -I\"D:/TI/TI CAR/BSP/KEY\" -I\"D:/TI/TI CAR/BSP/MENU\" -I\"D:/TI/TI CAR/BSP/UART\" -I\"D:/TI/TI CAR/BSP/PID\" -I\"D:/TI/TI CAR/BSP/ENCOUNDER\" -I\"D:/TI/TI CAR/BSP/MOTOR\" -I\"D:/TI/TI CAR/BSP/MPU6050\" -I\"D:/TI/TI CAR/BSP/SCAN\" -I\"D:/TI/TI CAR/BSP/OLED\" -I\"D:/TI/TI CAR/Debug\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"D:/TI/ccs/mspm0_sdk_2_05_01_00/source\" -I\"D:/TI/TI CAR/BSP/IIC\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"D:/TI/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "D:/TI/TI CAR/Debug/ti_msp_dl_config.c"}]