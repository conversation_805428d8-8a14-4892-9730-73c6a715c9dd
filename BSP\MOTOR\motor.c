#include "ti_msp_dl_config.h"
#include "board.h"
#include "motor.h"



void motor_init(void)
{
    DL_GPIO_clearPins(R_PORT, R_RA_PIN);
    DL_GPIO_clearPins(R_PORT, R_RB_PIN);
    DL_GPIO_clearPins(L_LA_PORT, L_LA_PIN);
    DL_GPIO_clearPins(L_LB_PORT, L_LB_PIN);   
}

void motor_setspeed(signed int target_speed_L,signed int target_speed_R)
{
    if(target_speed_L >= 0)
    {
        DL_GPIO_setPins(L_LB_PORT, L_LB_PIN);
        DL_GPIO_clearPins(L_LA_PORT, L_LA_PIN);
        DL_TimerG_setCaptureCompareValue(PWM_0_INST,target_speed_L,GPIO_PWM_0_C1_IDX);
    }
    else 
    {
        DL_GPIO_clearPins(L_LB_PORT, L_LB_PIN);
        DL_GPIO_setPins(L_LA_PORT, L_LA_PIN);
        DL_TimerG_setCaptureCompareValue(PWM_0_INST,-target_speed_L,GPIO_PWM_0_C1_IDX);  
    }

    if(target_speed_R >= 0)
    {
        DL_GPIO_clearPins(R_PORT, R_RA_PIN);
        DL_GPIO_setPins(R_PORT, R_RB_PIN);
        DL_TimerG_setCaptureCompareValue(PWM_0_INST,target_speed_R,GPIO_PWM_0_C0_IDX);
    }
    else 
    {   
        DL_GPIO_setPins(R_PORT, R_RA_PIN);
        DL_GPIO_clearPins(R_PORT, R_RB_PIN);
        DL_TimerG_setCaptureCompareValue(PWM_0_INST,-target_speed_R,GPIO_PWM_0_C0_IDX);  
    }
}



